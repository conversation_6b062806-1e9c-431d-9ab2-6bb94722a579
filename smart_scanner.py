import ccxt.async_support as ccxt_async
import asyncio
import time
from collections import defaultdict

# ALL exchanges as specified
SMART_EXCHANGES = [
    "ascendex",
    "bigone",
    "binance",
    "bitmart",
    "bitmex",
    "bitrue",
    "bitteam",
    "blockchaincom",
    "btcalpha",
    "btcturk",
    "cex",
    "coinbase",
    "coincatch",
    "coinsph",
    "cryptocom",
    "cryptomus",
    "digifinex",
    "hashkey",
    "hollaex",
    "kraken",
    "latoken",
    "lbank",
    "mexc",
    "myokx",
    "novadax",
    "oceanex",
    "okx",
    "p2b",
    "phemex",
    "poloniex",
    "probit",
    "tokocrypto",
    "tradeogre",
    "woo",
    "xt",
    "yobit"
]

# Exchange pool and market data
exchange_pool = {}
exchange_markets = {}

async def init_exchange_with_markets(exchange_name):
    """Initialize exchange and load its markets"""
    try:
        exchange_class = getattr(ccxt_async, exchange_name)
        exchange = exchange_class({
            'enableRateLimit': False,
            'timeout': 5000,
            'options': {'defaultType': 'spot'},
        })

        # Load markets to know which tokens exist
        markets = await exchange.load_markets()

        # Extract USDT pairs
        usdt_tokens = set()
        for symbol in markets:
            if '/USDT' in symbol and markets[symbol]['active']:
                token = symbol.split('/')[0]
                usdt_tokens.add(token)

        exchange_pool[exchange_name] = exchange
        exchange_markets[exchange_name] = usdt_tokens

        print(f"✓ {exchange_name}: {len(usdt_tokens)} USDT tokens")
        return True

    except Exception as e:
        print(f"✗ {exchange_name}: {e}")
        return False

async def init_smart_exchanges():
    """Initialize exchanges and build smart token mapping"""
    print("🧠 Smart Exchange Initialization...")

    tasks = [init_exchange_with_markets(ex) for ex in SMART_EXCHANGES]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    successful = sum(1 for r in results if r is True)
    print(f"📊 {successful}/{len(SMART_EXCHANGES)} exchanges ready")

    # Build smart token-to-exchange mapping
    token_exchange_map = defaultdict(list)
    for exchange_name, tokens in exchange_markets.items():
        for token in tokens:
            token_exchange_map[token].append(exchange_name)

    # Filter to tokens on multiple exchanges
    multi_exchange_tokens = {
        token: exchanges for token, exchanges in token_exchange_map.items()
        if len(exchanges) >= 2
    }

    print(f"🎯 Found {len(multi_exchange_tokens)} tokens on multiple exchanges")

    return multi_exchange_tokens

async def fetch_smart(exchange_name, symbol, semaphore):
    """Smart fetch - only called for tokens that exist on the exchange"""
    async with semaphore:
        try:
            exchange = exchange_pool[exchange_name]

            # Try ticker first (fastest)
            try:
                ticker = await asyncio.wait_for(exchange.fetch_ticker(symbol), timeout=3.0)
                if ticker.get('bid') and ticker.get('ask'):
                    return {
                        'exchange': exchange_name, 'symbol': symbol,
                        'bid': ticker['bid'], 'ask': ticker['ask'],
                        'timestamp': time.time(), 'error': None, 'method': 'ticker'
                    }
            except:
                pass

            # Fallback to orderbook with exchange-specific limit
            try:
                # Use exchange-specific orderbook limit (binance needs limit > 1)
                config = EXCHANGE_CONFIGS.get(exchange_name, {})
                limit = config.get('orderbook_limit', 1)
                orderbook = await asyncio.wait_for(exchange.fetch_order_book(symbol, limit=limit), timeout=4.0)
                if orderbook.get('bids') and orderbook.get('asks'):
                    return {
                        'exchange': exchange_name, 'symbol': symbol,
                        'bid': orderbook['bids'][0][0], 'ask': orderbook['asks'][0][0],
                        'timestamp': time.time(), 'error': None, 'method': 'orderbook'
                    }
            except Exception as e:
                return {
                    'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                    'timestamp': time.time(), 'error': str(e)[:100], 'method': 'failed'
                }

        except Exception as e:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(e)[:100], 'method': 'failed'
            }

        # Fallback return (should never reach here)
        return {
            'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
            'timestamp': time.time(), 'error': 'No data available', 'method': 'no_data'
        }

# Exchange-specific configurations for problematic exchanges
EXCHANGE_CONFIGS = {
    'bitmex': {'timeout_multiplier': 3.0, 'max_retries': 4, 'delay': 2.0, 'prefer_orderbook': True},
    'myokx': {'timeout_multiplier': 3.0, 'max_retries': 2, 'delay': 3.0, 'prefer_ticker': True, 'low_concurrency': True},
    'coincatch': {'timeout_multiplier': 2.0, 'max_retries': 3, 'delay': 1.0, 'prefer_ticker': True},
    'digifinex': {'timeout_multiplier': 2.0, 'max_retries': 3, 'delay': 1.0, 'prefer_ticker': True},
    'woo': {'timeout_multiplier': 2.5, 'max_retries': 3, 'delay': 1.5, 'prefer_orderbook': True},
    'btcalpha': {'timeout_multiplier': 3.0, 'max_retries': 4, 'delay': 2.0, 'prefer_orderbook': True},
    'hashkey': {'timeout_multiplier': 2.0, 'max_retries': 3, 'delay': 1.0, 'prefer_ticker': True},
    'binance': {'timeout_multiplier': 1.5, 'max_retries': 3, 'delay': 0.5, 'prefer_ticker': True, 'orderbook_limit': 5},
}

# Known problematic token-exchange combinations (to reduce "market not found" errors)
TOKEN_EXCHANGE_BLACKLIST = {
    'bitmex': {'DOT', 'BERA', 'BNB', 'BCH', 'DOGE', 'XMR', 'BSV'},
    'woo': {'XMR', 'CRO', 'AI16Z', 'YFI', 'THETA'},
    'coincatch': {'VIRTUAL', 'ALGO', 'FARTCOIN', 'AI16Z', 'PENGU'},
    'digifinex': {'APE', 'ALGO', 'BNB'},
    'hashkey': {'BCH'},
    'myokx': {'VIRTUAL', 'FARTCOIN', 'AI16Z', 'VANA'},
    'xt': {'YFI', 'GAL', 'XDC', 'DGB', 'FTM'},
    'phemex': {'USTC', 'FLOW', 'XCN', 'WAXP'},
    'lbank': {'STX', 'RUNE', 'ZRX'},
}

def is_token_blacklisted(exchange_name, token):
    """Check if a token is blacklisted for a specific exchange"""
    blacklist = TOKEN_EXCHANGE_BLACKLIST.get(exchange_name, set())
    return token in blacklist

async def retry_failed_requests_multi_round(failed_requests, max_concurrent=30, max_rounds=3):
    """Enhanced retry with multiple rounds and exchange-specific strategies"""
    print(f"🔄 Enhanced retry strategy: {max_rounds} rounds, {max_concurrent} concurrent requests")

    all_retry_results = []
    remaining_requests = failed_requests.copy()

    for round_num in range(1, max_rounds + 1):
        if not remaining_requests:
            break

        print(f"\n🔄 Retry Round {round_num}: {len(remaining_requests)} requests")

        # Adjust strategy per round
        round_concurrent = max(10, max_concurrent // round_num)  # Reduce concurrency each round
        base_delay = round_num * 0.5  # Increase delay each round

        semaphore = asyncio.Semaphore(round_concurrent)
        retry_tasks = []

        for exchange_name, symbol in remaining_requests:
            task = fetch_smart_retry_enhanced(exchange_name, symbol, semaphore, round_num, base_delay)
            retry_tasks.append(task)

        # Execute retries for this round
        round_results = []
        completed = 0
        total_retries = len(retry_tasks)

        print(f"🔄 Round {round_num} progress: ", end="", flush=True)

        for coro in asyncio.as_completed(retry_tasks):
            try:
                result = await coro
                if result is not None:
                    round_results.append(result)
                completed += 1

                if completed % max(1, total_retries // 10) == 0:
                    progress = (completed / total_retries) * 100
                    print(f"{progress:.0f}%", end=" ", flush=True)
            except Exception as e:
                completed += 1
                round_results.append({
                    'exchange': 'unknown', 'symbol': 'unknown', 'bid': None, 'ask': None,
                    'timestamp': time.time(), 'error': f"Round {round_num} exception: {str(e)[:30]}",
                    'method': f'retry_round_{round_num}_failed'
                })

        print()

        # Count successful retries for this round
        successful_round = [r for r in round_results
                           if r.get('error') is None and r.get('bid') and r.get('ask')]

        print(f"✅ Round {round_num} completed: {len(successful_round)}/{total_retries} successful")

        all_retry_results.extend(round_results)

        # Update remaining requests (remove successful ones)
        successful_keys = {(r['exchange'], r['symbol']) for r in successful_round}
        remaining_requests = [(ex, sym) for ex, sym in remaining_requests
                             if (ex, sym) not in successful_keys]

        if remaining_requests:
            print(f"🔄 {len(remaining_requests)} requests still need retry")
            # Add progressive delay between rounds
            if round_num < max_rounds:
                await asyncio.sleep(1.0 * round_num)

    # Final summary
    total_successful = len([r for r in all_retry_results
                           if r.get('error') is None and r.get('bid') and r.get('ask')])

    print(f"✅ Multi-round retry completed: {total_successful}/{len(failed_requests)} total successful")

    return all_retry_results

def analyze_failure_patterns(results):
    """Analyze failure patterns to help debug issues with detailed error reporting"""
    print(f"\n🔍 DETAILED FAILURE ANALYSIS:")

    # Group failures by exchange and collect detailed errors
    exchange_failures = defaultdict(list)
    error_types = defaultdict(int)
    detailed_errors = defaultdict(int)

    for result in results:
        if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
            exchange = result.get('exchange', 'unknown')
            error = result.get('error', 'no_data')
            method = result.get('method', 'unknown')
            symbol = result.get('symbol', 'unknown')

            exchange_failures[exchange].append({
                'symbol': symbol,
                'error': error,
                'method': method
            })

            # Count detailed error messages
            detailed_errors[str(error)] += 1

            # Categorize error types
            error_str = str(error).lower()
            if 'timeout' in error_str or 'timed out' in error_str:
                error_types['timeout'] += 1
            elif 'rate' in error_str or 'limit' in error_str:
                error_types['rate_limit'] += 1
            elif 'connection' in error_str or 'network' in error_str:
                error_types['network'] += 1
            elif 'not found' in error_str or '404' in error_str:
                error_types['not_found'] += 1
            elif 'does not have market' in error_str:
                error_types['market_not_found'] += 1
            elif 'invalid symbol' in error_str or 'bad symbol' in error_str:
                error_types['invalid_symbol'] += 1
            elif error == 'no_data' or 'no data' in error_str:
                error_types['no_data'] += 1
            elif 'permission' in error_str or 'unauthorized' in error_str:
                error_types['permission'] += 1
            elif 'server error' in error_str or '500' in error_str:
                error_types['server_error'] += 1
            else:
                error_types['other'] += 1

    # Print detailed error messages (top 20)
    print(f"📊 Top Error Messages:")
    sorted_errors = sorted(detailed_errors.items(), key=lambda x: x[1], reverse=True)
    for i, (error_msg, count) in enumerate(sorted_errors[:20]):
        print(f"   {i+1:2d}. [{count:3d}x] {error_msg}")

    # Print error type summary
    print(f"\n📊 Error Categories:")
    for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
        print(f"   {error_type}: {count}")

    # Print worst performing exchanges with detailed errors
    print(f"\n🏢 Exchanges with most failures (detailed):")
    sorted_failures = sorted(exchange_failures.items(), key=lambda x: len(x[1]), reverse=True)
    for exchange, failures in sorted_failures[:8]:  # Top 8 worst
        if len(failures) > 3:  # Only show exchanges with significant failures
            print(f"\n   {exchange}: {len(failures)} failures")

            # Group errors for this exchange
            exchange_errors = defaultdict(list)
            for failure in failures:
                error = failure['error'] if failure['error'] else 'no_data'
                exchange_errors[error].append(failure['symbol'])

            # Show top errors for this exchange
            sorted_exchange_errors = sorted(exchange_errors.items(), key=lambda x: len(x[1]), reverse=True)
            for error, symbols in sorted_exchange_errors[:5]:  # Top 5 errors per exchange
                print(f"     - [{len(symbols):2d}x] {error}")
                if len(symbols) <= 3:  # Show symbols if few
                    print(f"       Symbols: {', '.join(symbols)}")
                else:  # Show sample if many
                    print(f"       Sample symbols: {', '.join(symbols[:3])}...")

    # Suggest improvements
    print(f"\n💡 SUGGESTIONS:")
    if error_types.get('timeout', 0) > error_types.get('other', 0):
        print("   - Consider increasing timeouts for problematic exchanges")
    if error_types.get('rate_limit', 0) > 10:
        print("   - Consider reducing concurrency or adding delays")
    if error_types.get('network', 0) > 10:
        print("   - Network issues detected - consider additional retry rounds")
    if error_types.get('market_not_found', 0) > 50:
        print("   - Many 'market not found' errors - improve token filtering")
    if error_types.get('invalid_symbol', 0) > 20:
        print("   - Invalid symbol errors - check symbol formatting")

async def fetch_smart_retry_enhanced(exchange_name, symbol, semaphore, round_num, base_delay):
    """Enhanced retry with exchange-specific strategies and progressive timeouts"""
    async with semaphore:
        # Add progressive delay before attempting
        if base_delay > 0:
            await asyncio.sleep(base_delay)

        exchange = exchange_pool.get(exchange_name)
        if not exchange:
            return {
                'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': 'Exchange not available',
                'method': f'retry_round_{round_num}_unavailable'
            }

        # Get exchange-specific configuration
        config = EXCHANGE_CONFIGS.get(exchange_name, {
            'timeout_multiplier': 1.5, 'max_retries': 2, 'delay': 0.5, 'prefer_orderbook': False
        })

        # Calculate timeouts based on round and exchange config
        base_timeout = 3.0 + (round_num * 1.0)  # Increase timeout each round
        ticker_timeout = base_timeout * config['timeout_multiplier']
        orderbook_timeout = (base_timeout + 1.0) * config['timeout_multiplier']

        # Strategy selection based on exchange preference and round
        strategies = []
        if config.get('prefer_orderbook') or round_num > 1:
            strategies = ['orderbook', 'ticker']
        else:
            strategies = ['ticker', 'orderbook']

        last_error = None

        for strategy in strategies:
            try:
                if strategy == 'ticker':
                    ticker = await asyncio.wait_for(
                        exchange.fetch_ticker(symbol),
                        timeout=ticker_timeout
                    )
                    if ticker.get('bid') and ticker.get('ask'):
                        return {
                            'exchange': exchange_name, 'symbol': symbol,
                            'bid': ticker['bid'], 'ask': ticker['ask'],
                            'timestamp': time.time(), 'error': None,
                            'method': f'retry_round_{round_num}_ticker'
                        }

                elif strategy == 'orderbook':
                    # Use exchange-specific orderbook limit
                    limit = config.get('orderbook_limit', 1)
                    orderbook = await asyncio.wait_for(
                        exchange.fetch_order_book(symbol, limit=limit),
                        timeout=orderbook_timeout
                    )
                    if orderbook.get('bids') and orderbook.get('asks'):
                        return {
                            'exchange': exchange_name, 'symbol': symbol,
                            'bid': orderbook['bids'][0][0], 'ask': orderbook['asks'][0][0],
                            'timestamp': time.time(), 'error': None,
                            'method': f'retry_round_{round_num}_orderbook'
                        }

            except Exception as e:
                last_error = str(e)[:80]  # Capture more error detail
                continue  # Try next strategy

        # All strategies failed
        return {
            'exchange': exchange_name, 'symbol': symbol, 'bid': None, 'ask': None,
            'timestamp': time.time(),
            'error': f"Round {round_num} failed: {last_error}" if last_error else f"Round {round_num}: No data",
            'method': f'retry_round_{round_num}_failed'
        }

async def fetch_smart_retry(exchange_name, symbol, semaphore):
    """Legacy retry function for backward compatibility"""
    return await fetch_smart_retry_enhanced(exchange_name, symbol, semaphore, 1, 0.0)

async def smart_scan(token_exchange_map, max_concurrent=60, retry_failed=True):
    """Smart scan - only request tokens that exist on each exchange"""
    print(f"\n🧠 SMART SCAN: {max_concurrent} concurrent requests")
    print("🎯 Only requesting tokens that exist on each exchange")
    if retry_failed:
        print("🔄 Will retry failed requests after initial scan")

    semaphore = asyncio.Semaphore(max_concurrent)
    tasks = []
    total_requests = 0
    request_info = []  # Store request info for retries

    # Only create requests for tokens that actually exist on each exchange
    blacklisted_count = 0
    for token, exchanges in token_exchange_map.items():
        symbol = f"{token}/USDT"
        for exchange_name in exchanges:
            # Double-check the token exists on this exchange and is not blacklisted
            if (exchange_name in exchange_markets and
                token in exchange_markets[exchange_name] and
                not is_token_blacklisted(exchange_name, token)):
                task = fetch_smart(exchange_name, symbol, semaphore)
                tasks.append(task)
                request_info.append((exchange_name, symbol))  # Store for retries
                total_requests += 1
            elif is_token_blacklisted(exchange_name, token):
                blacklisted_count += 1

    print(f"📊 Smart requests: {total_requests:,}")
    print(f"📈 Tokens: {len(token_exchange_map)}")
    print(f"🏢 Average exchanges per token: {total_requests/len(token_exchange_map):.1f}")
    if blacklisted_count > 0:
        print(f"🚫 Blacklisted requests filtered: {blacklisted_count}")

    start_time = time.time()

    # Execute with progress
    print("\n🔄 Initial scan progress: ", end="", flush=True)
    completed = 0
    results = []
    failed_requests = []  # Store failed requests for retry

    # Execute all tasks and collect results
    all_results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results and identify failures
    for i, result in enumerate(all_results):
        completed += 1

        if isinstance(result, Exception):
            # Exception occurred
            error_result = {
                'exchange': request_info[i][0] if i < len(request_info) else 'unknown',
                'symbol': request_info[i][1] if i < len(request_info) else 'unknown',
                'bid': None, 'ask': None,
                'timestamp': time.time(), 'error': str(result)[:50], 'method': 'exception'
            }
            results.append(error_result)
            if i < len(request_info):
                failed_requests.append(request_info[i])
        elif result is not None:
            results.append(result)
            # Check if this request failed and should be retried
            if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
                if i < len(request_info):
                    failed_requests.append(request_info[i])

        if completed % max(1, total_requests // 20) == 0:
            progress = (completed / total_requests) * 100
            print(f"{progress:.0f}%", end=" ", flush=True)

    print()

    initial_successful = len([r for r in results if r.get('error') is None and r.get('bid') and r.get('ask')])

    print(f"📊 Initial scan: {initial_successful}/{total_requests} successful ({initial_successful/total_requests*100:.1f}%)")

    # Retry failed requests if enabled
    if retry_failed and failed_requests:
        print(f"\n🔄 Retrying {len(failed_requests)} failed requests...")
        retry_results = await retry_failed_requests_multi_round(failed_requests, max_concurrent // 2, max_rounds=3)

        # Replace failed results with retry results where successful
        retry_dict = {(r['exchange'], r['symbol']): r for r in retry_results
                     if r.get('error') is None and r.get('bid') and r.get('ask')}

        # Update results with successful retries
        for i, result in enumerate(results):
            if result.get('error') is not None or not (result.get('bid') and result.get('ask')):
                key = (result.get('exchange', 'unknown'), result.get('symbol', 'unknown'))
                if key in retry_dict:
                    results[i] = retry_dict[key]

        # Add any new successful results from retries
        for retry_result in retry_results:
            key = (retry_result['exchange'], retry_result['symbol'])
            if key in retry_dict and retry_result not in results:
                results.append(retry_result)

    end_time = time.time()
    duration = end_time - start_time

    # Analyze results
    successful = []
    method_counts = defaultdict(int)
    exchange_success = defaultdict(int)
    exchange_total = defaultdict(int)

    for result in results:
        method_counts[result.get('method', 'unknown')] += 1
        exchange_total[result['exchange']] += 1

        if result['error'] is None and result['bid'] and result['ask']:
            successful.append(result)
            exchange_success[result['exchange']] += 1

    success_rate = len(successful) / total_requests * 100

    print(f"✅ SMART scan completed in {duration:.2f} seconds")
    print(f"📊 Final Success: {len(successful):,}/{total_requests:,} ({success_rate:.1f}%)")

    if retry_failed and failed_requests:
        retry_improvement = len(successful) - initial_successful
        print(f"🔄 Retry improvement: +{retry_improvement} successful requests")
        print(f"📈 Success rate improvement: {initial_successful/total_requests*100:.1f}% → {success_rate:.1f}%")

    print(f"🚀 Speed: {total_requests/duration:.0f} requests/second")

    print(f"\n📈 Method breakdown:")
    for method, count in sorted(method_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"   {method}: {count:,}")

    print(f"\n🏢 Exchange performance:")
    for ex in sorted(exchange_success.keys()):
        if exchange_total[ex] > 0:
            rate = exchange_success[ex]/exchange_total[ex]*100
            print(f"   {ex}: {rate:.1f}% ({exchange_success[ex]}/{exchange_total[ex]})")

    # Add failure analysis for debugging
    if success_rate < 95.0:  # Only show if success rate is below 95%
        analyze_failure_patterns(results)

    return successful

def find_smart_arbitrage(results):
    """Find arbitrage with better filtering"""
    symbol_data = defaultdict(list)
    for result in results:
        symbol_data[result['symbol']].append(result)

    opportunities = []

    for symbol, exchange_data in symbol_data.items():
        if len(exchange_data) < 2:
            continue

        # Find best bid and ask
        best_bid = None
        best_ask = None
        best_bid_exchange = None
        best_ask_exchange = None

        for data in exchange_data:
            if data['bid'] and (best_bid is None or data['bid'] > best_bid):
                best_bid = data['bid']
                best_bid_exchange = data['exchange']

            if data['ask'] and (best_ask is None or data['ask'] < best_ask):
                best_ask = data['ask']
                best_ask_exchange = data['exchange']

        if (best_bid and best_ask and best_bid > best_ask and
            best_bid_exchange != best_ask_exchange):

            profit_pct = ((best_bid - best_ask) / best_ask) * 100

            # Filter realistic opportunities (remove obvious data errors)
            if 0.01 <= profit_pct <= 15:  # Between 0.01% and 15%
                opportunities.append({
                    'symbol': symbol,
                    'buy_exchange': best_ask_exchange,
                    'sell_exchange': best_bid_exchange,
                    'buy_price': best_ask,
                    'sell_price': best_bid,
                    'profit_percentage': profit_pct,
                    'profit_absolute': best_bid - best_ask,
                    'exchanges_count': len(exchange_data)
                })

    return sorted(opportunities, key=lambda x: x['profit_percentage'], reverse=True)

def display_smart_results(opportunities, top_n=30):
    """Display smart results"""
    print(f"\n🎯 TOP {min(top_n, len(opportunities))} SMART ARBITRAGE OPPORTUNITIES")
    print("=" * 100)

    if not opportunities:
        print("No arbitrage opportunities found.")
        return

    print(f"{'#':>2} {'SYMBOL':>12} {'BUY':>10} {'BUY PRICE':>12} {'SELL':>10} {'SELL PRICE':>12} {'PROFIT':>8} {'EXCH':>4}")
    print("-" * 100)

    for i, opp in enumerate(opportunities[:top_n], 1):
        print(f"{i:>2} {opp['symbol']:>12} {opp['buy_exchange']:>10} "
              f"${opp['buy_price']:>11.6f} {opp['sell_exchange']:>10} "
              f"${opp['sell_price']:>11.6f} {opp['profit_percentage']:>7.3f}% "
              f"{opp['exchanges_count']:>3}")

async def cleanup():
    """Close connections"""
    for exchange in exchange_pool.values():
        try:
            await exchange.close()
        except:
            pass

async def main():
    print("🧠 SMART ARBITRAGE SCANNER")
    print("=" * 50)
    print("✅ Only requests tokens that exist on each exchange")
    print("🎯 Dramatically improved success rates")
    print("⚡ Optimized for speed and accuracy")
    print("📊 Smart token-exchange mapping")
    print()

    try:
        # Smart initialization with market data
        token_exchange_map = await init_smart_exchanges()

        if not token_exchange_map:
            print("❌ No tokens found")
            return

        # Calculate smart requests
        total_smart_requests = sum(len(exchanges) for exchanges in token_exchange_map.values())
        print(f"📊 Smart requests: {total_smart_requests:,}")
        print(f"💡 vs Naive approach: {len(token_exchange_map) * len(exchange_pool):,} requests")
        reduction = (1 - total_smart_requests / (len(token_exchange_map) * len(exchange_pool))) * 100
        print(f"🎯 Request reduction: {reduction:.1f}%")

        # Smart scan
        results = await smart_scan(token_exchange_map, max_concurrent=60)

        if results:
            print(f"\n🎉 Got {len(results):,} successful results!")

            opportunities = find_smart_arbitrage(results)

            if opportunities:
                display_smart_results(opportunities, top_n=40)

                print(f"\n📈 SMART SUMMARY:")
                print(f"   Exchanges used: {len(exchange_pool)}")
                print(f"   Tokens scanned: {len(token_exchange_map)}")
                print(f"   Successful requests: {len(results):,}")
                print(f"   Arbitrage opportunities: {len(opportunities)}")

                if opportunities:
                    profits = [o['profit_percentage'] for o in opportunities]
                    print(f"   Average profit: {sum(profits)/len(profits):.3f}%")
                    print(f"   Maximum profit: {max(profits):.3f}%")
                    print(f"   Median profit: {sorted(profits)[len(profits)//2]:.3f}%")

                    # Realistic profit ranges
                    very_high = len([p for p in profits if p > 5.0])
                    high = len([p for p in profits if 1.0 <= p <= 5.0])
                    medium = len([p for p in profits if 0.1 <= p < 1.0])
                    low = len([p for p in profits if p < 0.1])

                    print(f"   Very high profit (>5%): {very_high}")
                    print(f"   High profit (1-5%): {high}")
                    print(f"   Medium profit (0.1-1%): {medium}")
                    print(f"   Low profit (<0.1%): {low}")
            else:
                print("No arbitrage opportunities found")
        else:
            print("❌ No successful requests")

    except KeyboardInterrupt:
        print("\n⚠️  Interrupted")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await cleanup()

if __name__ == "__main__":
    asyncio.run(main())
